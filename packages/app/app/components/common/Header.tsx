import { View } from "react-native";
import NotificationIcon from "@assets/svg/Notification";
import { useNavigation } from "@react-navigation/native";
import { RootNavParams } from "@navigation/root-navigator/RootNavParams";
import { StackNavigationProp } from "@react-navigation/stack";
import MyRabblesLogoHeader from "@assets/svg/MyRabblesLogoHeader";
import useAuthGuard from "@hooks/useAuthGuard";
import { trpc } from "@providers/RootProvider";
import { StyleSheet } from "react-native";
import { useEffect } from "react";
import { useAppProvider } from "../../providers/AppProvider";
import mixpanel from "@utils/mixpanel";
import { useSession } from "@hooks/persistUser";

type Props = {
  headerClasses?: string;
};

export default function Header(props: Props) {
  const navigation = useNavigation<StackNavigationProp<RootNavParams>>();
  const authGuard = useAuthGuard();
  const { user } = useSession();
  const { notficationCount, refetchNotificationCount } = useAppProvider();

  useEffect(() => {
    (async () => {
      refetchNotificationCount();
    })();
  }, []);
  return (
    <View
      className={[
        "relative flex-row justify-between items-center mt-2",
        props.headerClasses,
      ].join(" ")}
    >
      <View className="">
        <MyRabblesLogoHeader />
      </View>
      <View className="relative">
        <NotificationIcon
          onPress={() => {
            mixpanel.trackEvent(
              "Notifications checked",
              {
                email: user?.email || "",
                phone: user?.contact?.phone || "",
              },
              String(user?._id),
              "v2"
            );
            authGuard(() => navigation.navigate("NotificationScreen"));
          }}
        />
        {!!notficationCount && (
          <View
            style={styles.notificationDot}
            className="absolute right-0 top-0 w-[12px] h-[12px] rounded-lg"
          ></View>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  notificationDot: {
    shadowColor: "#004987",
    backgroundColor: "red",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
  },
});
