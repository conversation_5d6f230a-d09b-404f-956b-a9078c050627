import { Platform, SafeAreaView, StatusBar, ViewProps } from 'react-native';
import Header from './Header';
import clsx from 'clsx';
import { styled } from 'nativewind';

interface Props extends ViewProps {
  includeHeader?: boolean;
  headerClasses?: string;
  className?: string;
}

const Wrapper = styled(SafeAreaView, 'flex flex-1');

export default function Screen(props: Props) {
  return (
    <Wrapper
      style={{
        ...(Platform.OS === 'android' && {
          marginTop: StatusBar.currentHeight,
        }),
      }}
      {...props}
    >
      {props.includeHeader && <Header headerClasses={props.headerClasses} />}
      {props.children}
    </Wrapper>
  );
}
