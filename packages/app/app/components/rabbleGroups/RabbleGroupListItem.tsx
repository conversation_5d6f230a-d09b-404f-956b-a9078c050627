import Pressable from "@components/common/Pressable";
import { RouterOutput } from "../../../../shared";
import { Image } from "react-native-expo-image-cache";
import { imageKit } from "@utils/index";
import AppText from "@components/common/AppText";
import { Globe2, Lock } from "lucide-react-native";
import { Dimensions, View } from "react-native";

const { width } = Dimensions.get("screen");

export function RabbleGroupListItem({
  privacy,
  groupName,
  groupDescription,
  image,
  _id,
  onPress,
  isCareGiverManagedGroup,
}: RouterOutput["rabbleGroups"]["getUserCreatedGroups"][number] & {
  onPress: (group: string) => void;
  isCareGiverManagedGroup?: boolean;
}) {
  return (
    <Pressable
      actionTag="group details"
      className="flex-row items-center w-[94%] bg-white rounded-xl shadow-md shadow-primary/20 ml-4 mr-4"
      onPress={() => onPress(_id.toString())}
      style={{
        // width
      }}
    >
      {(
        <Image
          className="w-16 h-16 rounded-lg m-3"
          uri={imageKit({
            imagePath: image,
            transform: ["w-500"],
          })}
        />
      )}

      {/* Content */}
      <View className="flex-1 pr-3">
        <AppText className="text-base font-montserratMedium mb-1" numberOfLines={1}>
          {groupName}
        </AppText>
        <View className="flex-row items-center gap-2 mb-1">
          {privacy === "private" ? (
            <Lock color="#aeaeae" size={14} />
          ) : (
            <Globe2 color="#aeaeae" size={14} />
          )}
          <AppText className="text-xs text-neutral-500">{privacy}</AppText>
        </View>
        <AppText className="text-xs text-neutral-700" numberOfLines={2}>
          {groupDescription}
        </AppText>
      </View>
    </Pressable>
  );
}
