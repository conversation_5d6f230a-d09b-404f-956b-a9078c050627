import React from "react";
import { View, Text, TouchableOpacity, Modal } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import AppButton from "@components/common/AppButton";
import withPressAnimation from "@components/common/AnimateButton";
import Pressable from "@components/common/Pressable";
import Svg, { Path, SvgProps } from "react-native-svg";
import { useNavigation } from "@react-navigation/native";
import { RootNavigationProp } from "@navigation/root-navigator";
import { useSession } from "@hooks/persistUser";
import mixpanel from "@utils/mixpanel";

function ContactsIcon(props: SvgProps) {
  return (
    <Svg width={96} height={96} viewBox="0 0 96 96" fill="none" {...props}>
      <Path
        d="M94.777 48.024c0 25.735-21.085 46.606-47.106 46.606C21.651 94.63.565 73.759.565 48.024c0-25.735 21.085-46.606 47.106-46.606s47.106 20.871 47.106 46.606z"
        fill="#fff"
        stroke="#B4DDFF"
      />
      <Path
        d="M65.473 45.52c4.144-.576 7.334-4.09 7.343-8.35 0-4.197-3.093-7.678-7.147-8.336M70.941 55.374c4.014.594 6.815 1.984 6.815 4.85 0 1.972-1.32 3.254-3.452 4.06"
        stroke="#004987"
        strokeWidth={1.2}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M47.647 56.592c-9.548 0-17.702 1.431-17.702 7.148 0 5.714 8.104 7.187 17.702 7.187 9.547 0 17.698-1.417 17.698-7.137s-8.1-7.198-17.698-7.198zM47.647 48.432c6.265 0 11.345-5.023 11.345-11.225 0-6.2-5.08-11.226-11.345-11.226-6.265 0-11.344 5.027-11.344 11.226-.024 6.178 5.017 11.205 11.261 11.225h.083z"
        fill="#004987"
      />
      <Path
        d="M29.818 45.52c-4.147-.576-7.335-4.09-7.343-8.35 0-4.197 3.092-7.678 7.147-8.336M24.35 55.374c-4.014.594-6.815 1.984-6.815 4.85 0 1.972 1.32 3.254 3.452 4.06"
        stroke="#004987"
        strokeWidth={1.2}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
}

const AnimatedAppButton = withPressAnimation(AppButton);

type Props = {
  visible: boolean;
  onClose: () => void;
};

const CreateRabblePopup = ({ visible, onClose }: Props) => {
  const navigation = useNavigation<RootNavigationProp>();
  const { user } = useSession();

  return (
    <Modal transparent visible={visible} animationType="slide">
      <Pressable onPress={onClose} className="flex-1 bg-black/40" />

      <View className="absolute bottom-0 w-full bg-white rounded-t-3xl px-6 pt-6 pb-8 shadow-2xl h-[85%]">
        <Pressable onPress={onClose} className="absolute top-4 right-4 p-2">
          <Ionicons name="close" size={25} color="#1D6FE8" />
        </Pressable>

        <View className="flex-1 justify-between mt-8">
          <View className="items-center">
            <View className="bg-[#E1F1FF] border-[1px] border-[#B4DDFF] px-36 py-20 rounded-xl flex-row items-center justify-center space-x-3 mb-6 relative">
              <View className="absolute -left-8 -top-8">
                {/* <Text className="text-[64px]">👥</Text> */}
                <ContactsIcon />
              </View>
              <View className="absolute right-0 top-0">
                <Text className="text-[64px]">🫶</Text>
              </View>
              <View className="absolute left-0 bottom-0">
                <Text className="text-[64px]">🔔</Text>
              </View>
              <View className="absolute border-[1px] border-[#B4DDFF] -bottom-4 -right-4 bg-white rounded-full px-2 py-1 shadow-md">
                <Text className="text-[32px] text-[#336D9F]">👍🏾 3 👍🏼 2</Text>
              </View>
            </View>

            <Text className="text-[35px] leading-[45.5px] font-bold text-center text-[#336D9F] font-montserrat mb-2 mt-6">
              How to create{"\n"}a Rabble
            </Text>

            <Text className="text-[14px] text-center w-[312px] leading-[15.6px] font-normal text-[#336D9F] font-montserrat mt-4">
              Invite your support community, bring together a group of
              advocates, and more! Create topic-based rabbles and connect with
              others in our community.
            </Text>
          </View>

          <View className="mt-0 mb-2">
            <AnimatedAppButton
              btnContainer="flex flex-row"
              textClassName="text-[21px]"
              title="GET STARTED"
              variant="new-primary"
              className="w-full rounded-full"
              onPress={() => {
                onClose();
                mixpanel.trackEvent(
                  "Rabble get started clicked (Step 1)(Inviting)",
                  {
                    email_or_phone: user?.email || user?.contact?.phone || "",
                  },
                  user?._id?.toString(),
                  "v2"
                );
                navigation.navigate("CreateNewRabble");
              }}
              style={{
                shadowColor: "#003366",
                shadowOffset: { width: 0, height: 3 },
                shadowOpacity: 1,
                shadowRadius: 1,
                elevation: 5,
              }}
            />
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default CreateRabblePopup;
