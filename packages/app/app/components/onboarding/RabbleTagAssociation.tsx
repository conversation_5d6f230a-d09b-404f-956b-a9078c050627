import ButterFlyIcon from "@assets/svg/ButterFlyIcon";
import QuestionPrompt from "@assets/svg/QuestionPrompt";
import ProgressBar from "@components/common/ProgressBar";
import Screen from "@components/common/Screen";
import { ChevronLeft } from "lucide-react-native";
import React from "react";
import { Dimensions, View, Text, Platform } from "react-native";

import { RouteProp, useNavigation, useRoute } from "@react-navigation/native";
import { RootNavigationProp } from "@navigation/root-navigator";
import withPressAnimation from "@components/common/AnimateButton";

import AppFormTagsAutocomplete from "@components/form/AppFormTagsAutocomplete";
import { FormProvider, useForm } from "react-hook-form";
import { z } from "zod";
import errorHandler from "@utils/errorhandler";
import { zodResolver } from "@hookform/resolvers/zod";
import { trpc } from "@providers/RootProvider";
import { useSession } from "@hooks/persistUser";
import AppScrollView from "@components/common/AppScrollView";
import AppButton from "@components/common/AppButton";
import Svg, {
  Path,
  Pattern,
  Use,
  SvgProps,
  Defs,
  Image,
} from "react-native-svg";

import { RootNavParams } from "@navigation/root-navigator/RootNavParams";
import mixpanel from "@utils/mixpanel";
import _ from "lodash";

function TagIcon(props: SvgProps) {
  return (
    <Svg width={60} height={61} viewBox="0 0 60 61" fill="none" {...props}>
      <Path fill="url(#pattern0_10483_20595)" d="M0 0H60V61H0z" />
      <Defs>
        <Pattern
          id="pattern0_10483_20595"
          patternContentUnits="objectBoundingBox"
          width={1}
          height={1}
        >
          <Use
            xlinkHref="#image0_10483_20595"
            transform="scale(.01667 .0164)"
          />
        </Pattern>
        <Image
          id="image0_10483_20595"
          width={60}
          height={61}
          preserveAspectRatio="none"
          xlinkHref="data:image/png;base64,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"
        />
      </Defs>
    </Svg>
  );
}

const AnimatedAppButton = withPressAnimation(AppButton);

const screenWidth = Dimensions.get("window").width;
const cardWidth = screenWidth * 0.9;

const schema = z.object({
  tags: z.array(z.string()).default([]),
});

type Form = z.infer<typeof schema>;

export default function RabbleTagAssociation() {
  const navigation = useNavigation<RootNavigationProp>();
  const { groupId } =
    useRoute<RouteProp<RootNavParams, "RabbleTagAssociation">>()?.params;
  const { user } = useSession();
  const methods = useForm<Form>({
    resolver: zodResolver(schema),
    defaultValues: {
      tags: [],
    },
  });

  const { mutateAsync: updatedRabbleGroup } =
    trpc.rabbleGroups.updateRabbleGroupPatials.useMutation();

  const groupInfo = trpc.rabbleGroups.getGroupInfo.useQuery(
    { group: groupId },
    { enabled: !!groupId }
  );

  const { handleSubmit } = methods;

  const onSubmit = handleSubmit(async (data) => {
    try {
      const tags = data.tags || [];

      await updatedRabbleGroup({
        id: String(groupId),
        tags,
      });
      mixpanel.trackEvent(
        "Rabble community tags selected (Step 4)(Inviting)",
        {
          community_name: groupInfo?.data?.groupName || "",
          community_desc: groupInfo?.data?.groupDescription || "",
          community_guideline: groupInfo?.data?.groupGuidelines || "",
          community_status: groupInfo?.data?.privacy || "",
          email_or_phone: user?.email || user?.contact?.phone || "",
          tags: _.join(tags, ","),
        },
        user?._id?.toString(),
        "v2"
      );
      navigation.navigate("OnboardingContacts", { groupId, groupTags: tags });
    } catch (ex) {
      errorHandler(ex);
    }
  });

  return (
    <Screen>
      <AppScrollView>
        <View className="flex-row items-center mt-8">
          <ChevronLeft
            color={"#004987"}
            size={22}
            className="ml-4"
            onPress={() => navigation.goBack()}
          />
          <ProgressBar
            total={3}
            current={1}
            style={{ marginLeft: 8, flex: 1, marginRight: 16 }}
          />
        </View>
        <View className="flex-1 items-center mt-[-12px]">
          <View className="flex-row items-center">
            <ButterFlyIcon height={140} width={90} />
            <QuestionPrompt message="Are there any tags we can apply?" />
          </View>
          <View className="bg-[#E1F1FF] p-4 h-[91px] w-[94px] rounded-xl items-center">
            <TagIcon />
          </View>
          <Text className="mb-4 mt-2 text-center text-[#336D9F] font-montserratBold text-[12px] leading-[130%]">
            Tags
          </Text>
          <View
            className="flex-1 p-2 mt-[-12px]"
            style={{ width: cardWidth, gap: 8 }}
          >
            <Text
              className="text-[#004987] mb-2 mt-4"
              style={{
                fontFamily: "Montserrat_400Regular",
                fontSize: Platform.OS === "android" ? 20 : 18,
                fontWeight: Platform.OS === "android" ? "500" : "500",
                lineHeight: 24,
              }}
            >
              Are there additional tags you would like to associate with this
              Rabble?
            </Text>
            <FormProvider {...methods}>
              {/* Enhanced Tags Input with Autocomplete */}
              <AppFormTagsAutocomplete
                name="tags"
                label="Tags"
                placeholder="Type to search or create tags..."
                maxTags={10}
                className="mb-4"
              />
            </FormProvider>
          </View>
        </View>
      </AppScrollView>
      <View className="flex flex-row items-center justify-between mb-2 mx-4">
        <AnimatedAppButton
          btnContainer="flex flex-row p-1"
          title="CONTINUE"
          variant={"new-primary"}
          className="flex-1 rounded-full"
          textClassName="text-[21px]"
          style={{
            shadowColor: "#003366",
            shadowOffset: { width: 0, height: 3 },
            shadowOpacity: 1,
            shadowRadius: 1,
            elevation: 5,
          }}
          onPress={onSubmit}
        />
      </View>
    </Screen>
  );
}
