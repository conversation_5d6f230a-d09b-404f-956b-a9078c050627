import ButterFlyIcon from "@assets/svg/ButterFlyIcon";
import QuestionPrompt from "@assets/svg/QuestionPrompt";
import ProgressBar from "@components/common/ProgressBar";
import Screen from "@components/common/Screen";
import { ChevronLeft } from "lucide-react-native";
import React, { useEffect, useState } from "react";
import { Dimensions, View, Text } from "react-native";
import AppButton from "@components/common/AppButton";
import { RouteProp, useNavigation, useRoute } from "@react-navigation/native";
import { RootNavigationProp } from "@navigation/root-navigator";
import withPressAnimation from "@components/common/AnimateButton";
import { FormProvider, useForm } from "react-hook-form";
import { z } from "zod";
import errorHandler from "@utils/errorhandler";
import { zodResolver } from "@hookform/resolvers/zod";
import AppText from "@components/common/AppText";
import AppFormOtpInput from "@components/form/AppFormOtpInput";
import { trpc } from "@providers/RootProvider";
import { useInterval } from "@hooks/useInterval";
import { emailRegex } from "./SignupScreenV1";
import { RootNavParams } from "@navigation/root-navigator/RootNavParams";
import usePersistedUser from "@hooks/persistUser";
import clsx from "clsx";
import OnboardingBuilding from "@components/onboarding/OnboardingBuilding";
import AppScrollView from "@components/common/AppScrollView";
import _ from "lodash";
import useScreenTracking from "@hooks/useScreenTracking";
import mixpanel from "@utils/mixpanel";
import { MIXPANEL_TOKEN_V2 } from "@constants/index";
import { RABBLEGROUP_TYPES } from "../../../../shared/validators/rabblegroup.validator";
import { createOrReadAppInstanceId } from "@hooks/useAppInstanceId";
import { isAxiosError } from "axios";

const AnimatedAppButton = withPressAnimation(AppButton);

const screenWidth = Dimensions.get("window").width;
const cardWidth = screenWidth * 0.9;

const schema = z.object({
  otp: z.string().min(1, "Otp is required"),
});

type Form = z.infer<typeof schema>;

export default function SignupOtpScreenV1() {
  const navigation = useNavigation<RootNavigationProp>();
  // otp timers
  const [otpTimer, setOtpTimer] = useState(60);
  const [isBuildingScreen, setIsBuildingScreen] = useState(false);
  useInterval(() => setOtpTimer((_) => _ - 1), otpTimer <= 0 ? null : 1000);

  const { setOnboardingProgressScreen } = useScreenTracking();
  const { mutateAsync: saveProgressScreen } = setOnboardingProgressScreen;

  const { phoneOrEmail, deviceId = "" } =
    useRoute<RouteProp<RootNavParams, "SignupOtpScreenV1">>().params;

  const methods = useForm<Form>({
    resolver: zodResolver(schema),
    defaultValues: {
      otp: "",
    },
  });

  const { handleSubmit } = methods;

  const { requestLoginOtp, validateLoginOtp } = trpc.auth;

  const { mutateAsync: requestOtpAsync } = requestLoginOtp.useMutation();
  const { mutateAsync: validateLoginOtpAsync, isLoading: validatingUser } =
    validateLoginOtp.useMutation();
  const { mutateAsync: createRabbleGroup, isLoading: creatingRabbleGroup } =
    trpc.rabbleGroups.createRabbleGroup.useMutation();

  const updateUserPartial = trpc.user.updateUserPartial.useMutation();

  const resendOtp = async () => {
    try {
      setOtpTimer(60);
      await requestOtpAsync({
        ...(emailRegex.test(phoneOrEmail)
          ? { email: phoneOrEmail }
          : { phone: phoneOrEmail }),
        // deviceId: deviceId || "",
      });
    } catch (ex) {
      const { message } = errorHandler(ex);
      methods.setError("otp", { message: message });
    }
  };

  const { setPersistedUser, setUser, user } = usePersistedUser();
  const { data: userData } = user;
  const { mutateAsync: saveUserAsync } = setUser;
  const { mutateAsync: savePersistedUser } = setPersistedUser;
  const onSubmit = handleSubmit(async (data) => {
    try {
      const user = await validateLoginOtpAsync(
        {
          otp: data.otp,
          ...(emailRegex.test(phoneOrEmail)
            ? { email: phoneOrEmail }
            : { phone: phoneOrEmail }),
        },
        {
          async onSuccess(user) {
            await mixpanel.createUserProfile({
              $distinct_id: String(user._id),
              // @ts-expect-error
              $set: _.mapValues(user, String),
              $token: MIXPANEL_TOKEN_V2 || "",
            });
            await mixpanel.trackEvent(
              "Account verified (Step 5)(Account Creation)",
              {
                email_or_phone: phoneOrEmail,
                verification_method: "Otp",
              },
              user?._id?.toString(),
              "v2"
            );
            await createRabbleGroup({
              groupDescription: `Welcome to ${user?.firstname}'s Rabble group! This is a space to share health experiences, connect with others, and build a supportive community.`,
              groupName: `${user?.firstname}'s Rabble`,
              privacy: RABBLEGROUP_TYPES.public,
            });

            const appInstanceId = await createOrReadAppInstanceId();
            try {
              await mixpanel.mergeIdentities(appInstanceId, String(user._id));
              console.log("merged identities", appInstanceId, user._id);
            } catch (ex) {
              if (isAxiosError(ex)) console.error(ex.response?.data);
            }
            navigation.navigate("OnBoardingCreateProfile", { user });
          },
        }
      );
    } catch (ex) {
      const { message } = errorHandler(ex);
      methods.setError("otp", { message: message });
    }
  });

  return (
    <Screen>
      <AppScrollView>
        <View className="flex-row items-center mt-8">
          <ChevronLeft
            color={"#004987"}
            size={22}
            className="ml-4"
            onPress={() => navigation.goBack()}
          />
          <ProgressBar
            total={5}
            current={3}
            style={{ marginLeft: 8, flex: 1, marginRight: 16 }}
          />
        </View>
        <View className="flex-1 items-center">
          <View className="flex-row items-center mb-8">
            <ButterFlyIcon height={140} width={90} />
            <QuestionPrompt message="Great! Please enter your magic code." />
          </View>

          <View
            className="flex-1 p-2 mt-[-56px]"
            style={{ width: cardWidth, gap: 8 }}
          >
            <FormProvider {...methods}>
              <AppFormOtpInput
                name="otp"
                customClass="border border-[#B4DDFF] border-b-[5px] border-b-[#B4DDFF]"
              />
              <AppText
                className={clsx(
                  otpTimer <= 0
                    ? "underline text-primary font-montserratBold color-[#004987]"
                    : "text-[#004987] font-manrope text-[14px] font-light leading-normal"
                )}
                onPress={!otpTimer ? resendOtp : undefined}
              >
                Resend code {otpTimer > 0 ? " in" : ""}
                {!!otpTimer && (
                  <AppText className="text-primary font-montserratMedium">
                    {" "}
                    {otpTimer}s{" "}
                  </AppText>
                )}
              </AppText>
              <View className="flex mt-40">
                <AnimatedAppButton
                  btnContainer="flex flex-row mb-2"
                  title="NEXT"
                  variant={"new-primary"}
                  className="flex-1 rounded-full"
                  textClassName="text-[21px]"
                  style={{
                    shadowColor: "#003366",
                    shadowOffset: { width: 0, height: 5 },
                    shadowOpacity: 1,
                    shadowRadius: 1,
                    elevation: 5,
                  }}
                  onPress={onSubmit}
                />
              </View>
            </FormProvider>
          </View>
        </View>
      </AppScrollView>
    </Screen>
  );
}
