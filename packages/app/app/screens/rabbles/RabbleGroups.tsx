import Logo from "@assets/svg/Logo";
import { useEffect } from "react";
import LogoFilled from "@assets/svg/LogoFilled";
import AppText from "@components/common/AppText";
import { FlatList, Platform, StyleSheet, Text, View } from "react-native";
import { Filter, Globe2, Lock, PlusCircle, Search } from "lucide-react-native";
import { trpc } from "@providers/RootProvider";
import { useFocusEffect, useNavigation } from "@react-navigation/native";
import { StackNavigationProp } from "@react-navigation/stack";
import { RootNavParams } from "@navigation/root-navigator/RootNavParams";
import Tabs from "@components/common/Tabs";
import { useCallback, useMemo, useState } from "react";
import { RouterOutput } from "../../../../shared";
import { Image } from "react-native-expo-image-cache";
import { imageKit } from "@utils/index";
import useAuthGuard from "@hooks/useAuthGuard";
import { useSession } from "@hooks/persistUser";
import _ from "lodash";
import { z } from "zod";
import { getDiscoverGroupsValidator } from "../../../../shared/validators/rabblegroup.validator";
import { FormProvider, useForm } from "react-hook-form";
import DiseaseTagsFilter from "app/elements/rabbles/DiseaseTagsFilter";
import Pressable from "@components/common/Pressable";
import { RabbleGroupListItem } from "@components/rabbleGroups/RabbleGroupListItem";
import mixpanel from "@utils/mixpanel";

import * as React from "react";
import Svg, { Circle, Path, SvgProps } from "react-native-svg";

function ContactIcon(props: SvgProps) {
  return (
    <Svg
      // xmlns="http://www.w3.org/2000/svg"
      width={40}
      height={40}
      viewBox="0 0 40 40"
      fill="none"
      {...props}
    >
      <Circle cx={19.9273} cy={19.9273} r={19.9273} fill="#fff" />
      <Path
        d="M27.379 18.87a3.576 3.576 0 003.074-3.533 3.575 3.575 0 00-2.992-3.527M29.668 23.037c1.68.251 2.852.84 2.852 2.052 0 .834-.552 1.376-1.445 1.717"
        stroke="#004987"
        strokeWidth={1.2}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M19.917 23.553c-3.996 0-7.41.605-7.41 3.024 0 2.417 3.393 3.04 7.41 3.04 3.997 0 7.409-.6 7.409-3.02 0-2.419-3.391-3.044-7.409-3.044zM19.917 20.1a4.748 4.748 0 10-4.748-4.748 4.73 4.73 0 004.714 4.749h.034z"
        fill="#004987"
      />
      <Path
        d="M12.454 18.87a3.574 3.574 0 01-3.074-3.533 3.575 3.575 0 012.992-3.527M10.165 23.037c-1.68.251-2.852.84-2.852 2.052 0 .834.552 1.376 1.445 1.717"
        stroke="#004987"
        strokeWidth={1.2}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
}

const schema = z.object({ diseaseTags: z.array(z.string()).optional() });

type Filter = z.infer<typeof schema>;
export default function RabbleGroups() {
  const [open, setOpen] = useState(false);

  const navigation = useNavigation<StackNavigationProp<RootNavParams>>();
  const [tab, setTab] = useState(0);
  const authGuard = useAuthGuard();
  const { user } = useSession();

  const methods = useForm<Filter>({ defaultValues: { diseaseTags: [] } });
  const diseaseTags = methods.watch("diseaseTags");

  const createRabbleGroup = () =>
    authGuard(() => navigation.navigate("CreateOrEditRabbleGroupScreen"));

  const createdGroups = trpc.rabbleGroups.getUserCreatedGroups.useQuery();
  const joinedGroups = trpc.rabbleGroups.userJoinedGroups.useQuery();
  // const patientGroups =
  //   trpc.rabbleGroups.caregiverPatientJoinedGroups.useQuery();
  const discoverGroups = trpc.rabbleGroups.discoverRabbleGroups.useQuery({
    diseaseTags: diseaseTags?.filter(Boolean) || [],
  });
  const getUserById = trpc.user.getUserById.useMutation();

  const combineGroups = useMemo(() => {
    const groups = _.uniqBy(
      [
        ...(createdGroups.data || []).map((_) => ({ ..._, owner: true })),
        ...(joinedGroups.data || []).map((_) => ({
          ..._.rabbleGroup,
        })),
        // ...(patientGroups.data || []).map((_) => ({
        //   ..._.rabbleGroup,
        //   isCareGiverManagedGroup: true,
        // })),
      ],
      "_id"
    );

    return groups;
  }, [createdGroups.data, joinedGroups.data]);

  const handleRabbleGroupSelect = useCallback(
    (group: string, isCareGiverManagedGroup?: boolean) => {
      navigation.navigate("RabbleDetailsScreen", {
        groupId: group,
        isCareGiverManagedGroup,
      });
    },
    []
  );

  useFocusEffect(
    useCallback(() => {
      createdGroups.refetch();
    }, [])
  );

  useFocusEffect(
    useCallback(() => {
      (async () => {
        console.log("is this event capturing", tab);

        if (tab === 1) {
          await mixpanel.trackEvent(
            "Discover Rabbles filter selected (Rabble screen)",
            {
              email: user?.email || "",
              phone: user?.contact?.phone || "",
            },
            String(user?._id),
            "v2"
          );
        } else if (tab === 0) {
          await mixpanel.trackEvent(
            "My Rabbles filter selected(Rabble screen)",
            {
              email: user?.email || "",
              phone: user?.contact?.phone || "",
            },
            String(user?._id),
            "v2"
          );
        }
      })();
    }, [tab])
  );

  const handleRabbleGroupSearch = () => {
    authGuard(() => navigation.navigate("SearchRabblesScreen"));
  };

  return (
    <View className="flex-1 p-4">
      {/* Header */}
      <View className="flex-row items-center justify-between">
        <View className="flex-row items-center gap-4">
          <View>
            <LogoFilled />
          </View>
          <AppText className="font-montserratSemiBold text-primary">
            My Rabbles
          </AppText>
        </View>

        <PlusCircle color="#023967" onPress={createRabbleGroup} />
      </View>
      {/* Search */}
      <Pressable
        actionTag="rabble groups search"
        className="border border-neutral-300 rounded-lg p-2 mt-4 flex-row items-center"
        style={{ gap: 8 }}
        onPress={handleRabbleGroupSearch}
      >
        <Search color="#acacac" />
        <AppText className="text-neutral-400 italic">search...</AppText>
      </Pressable>

      <Tabs
        tabState={[tab, setTab]}
        tabs={["Joined", "Explore"]}
        selectedbgClass="bg-[#0B79D3]"
        shadowStyle={
          Platform.OS !== "ios"
            ? {
                shadowColor: "#000000",
                shadowOffset: { width: 10, height: 10 },
                shadowOpacity: 6,
                shadowRadius: 6,
                elevation: 4,
              }
            : {
                shadowColor: "#000000",
                shadowOffset: { width: 0, height: 4 },
                shadowOpacity: 0.2,
                shadowRadius: 6,
                elevation: 8,
              }
        }
      />

      {tab === 1 && (
        <View className="flex-1 py-0">
          <AppText className="text-[20px] text-[#336D9F] mb-4">
            Getting Started...
          </AppText>
          <View className=" justify-between bg-white rounded-xl shadow-md shadow-primary/20 w-[200px]">
            <View className="items-center">
              <Text className="text-[20px] text-600 text-center text-[#336D9F] font-montserrat mb-2 mt-2">
                How to create a{"\n"} Rabble
              </Text>
              <View className="bg-[#E1F1FF] border-[1px] border-[#B4DDFF] px-10 py-10 rounded-xl flex-row items-center justify-center space-x-1 mb-6 relative">
                <View className="absolute -left-2 -top-4">
                  {/* <Text className="text-[24px]">👥</Text> */}
                  <ContactIcon />
                </View>
                <View className="absolute right-0 top-0">
                  <Text className="text-[32px]">🫶</Text>
                </View>
                <View className="absolute left-0 bottom-0">
                  <Text className="text-[24px]">🔔</Text>
                </View>
                <View className="absolute border-[1px] border-[#B4DDFF] bottom-2 -right-8 bg-white rounded-full px-2 py-1 shadow-md">
                  <Text className="text-[12px] text-[#336D9F]">👍🏾 3 👍🏼 2</Text>
                </View>
              </View>
            </View>
          </View>
        </View>
      )}

      <View className={`flex-1 pb-4 ${tab === 1 ? '-mt-20' : ''}`}>
        {tab === 0 && (
          <FlatList
            numColumns={2}
            data={combineGroups}
            contentContainerStyle={{
              gap: 12,
              marginTop: 16,
              marginBottom: 42,
            }}
            columnWrapperStyle={{ gap: 12 }}
            keyExtractor={(item) => item._id.toString()}
            renderItem={({ item }) => (
              <RabbleGroupListItem
                onPress={handleRabbleGroupSelect}
                {...item}
              />
            )}
          />
        )}
        {tab === 1 && (
          <>
            <AppText className="mt-4 text-[22px] leading-[28.6px] font-bold text-[#336D9F] font-montserrat">
              Explore
            </AppText>
            <FlatList
              numColumns={2}
              data={discoverGroups.data}
              contentContainerStyle={{
                gap: 12,
                marginTop: 16,
              }}
              columnWrapperStyle={{ gap: 12, marginBottom: 15 }}
              keyExtractor={(item) => item._id.toString()}
              renderItem={({ item }) => (
                <RabbleGroupListItem
                  {...item}
                  onPress={handleRabbleGroupSelect}
                />
              )}
            />
          </>
        )}
      </View>
    </View>
  );
}
