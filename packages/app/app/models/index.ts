export interface Pagination {
  currentPage: number;
  nextPage: number;
  prevPage: number | null;
  isLastPage: boolean;
  totalPages: number;
  totalItems: number;
}

export interface RabbleGroup {
  groupId: string;
  isCareGiverManagedGroup?: boolean;
  tags?: string[];
}

export type RabbleGroupUserRole = 'owner' | 'admin' | 'user';

export interface RabbleGroupMember {
  _id: string;
  rabbleGroup: string;
  role: RabbleGroupUserRole;
  user: {
    _id: string;
    externalId: number;
    createdAt: string;
    firstname: string;
    lastname: string;
    username: string;
    email?: string;
    profilePicture?: string;
  };
  createdAt: string;
}

export interface UserGroups {
  _id: string;
  createdAt: string;
  rabbleGroup: RabbleGroup;
  role: RabbleGroupUserRole;
  user: string;
}
