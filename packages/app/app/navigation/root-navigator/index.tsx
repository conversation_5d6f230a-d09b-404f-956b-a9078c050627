import NavigationHeader from "@components/navigation/NavigationHeader";
import { Entypo } from "@expo/vector-icons";
import AppNavigator from "@navigation/app-navigator";
import {
  type StackNavigationOptions,
  type StackNavigationProp,
  createStackNavigator,
} from "@react-navigation/stack";
import LoginSplashScreen from "@screens/login";
import LoginMobileScreen from "@screens/login/LoginMobileScreen";
import LoginOtpScreen from "@screens/login/LoginOtpScreen";
import SignupScreen from "@screens/signup/SignupScreen";
import WalkThroughScreen from "@screens/walkthrough";

import ManageLearningSceen from "@components/manage/ManageLearningSceen";
import CreateNewRabble from "@components/onboarding/CreateNewRabble";
import GroupPrivacyPreferences from "@components/onboarding/GroupPrivacyPreferences";
import OnBoardingWalkthroughScreen from "@components/onboarding/OnBoardingWalkthroughScreen";
import OnboardingBuilding from "@components/onboarding/OnboardingBuilding";
import OnboardingContactList from "@components/onboarding/OnboardingContactList";
import OnboardingContacts from "@components/onboarding/OnboardingContacts";
import OnBoardingCreateProfile from "@components/onboarding/OnboardingCreateProfile";
import OnboardingDailyCheckIn from "@components/onboarding/OnboardingDailyCheckIn";
import OnboardingDailyCheckInProcessed from "@components/onboarding/OnboardingDailyCheckInProcessed";
import OnboardingDailyCheckInQuestion from "@components/onboarding/OnboardingDailyCheckInQuestion";
import OnboardingDailyLearning from "@components/onboarding/OnboardingDailyLearning";
import OnboardingGoals from "@components/onboarding/OnboardingGoals";
import OnboardingLastQuestion from "@components/onboarding/OnboardingLastQuestion";
import OnboardingLearningQuestion from "@components/onboarding/OnboardingLearningQuestion";
import OnboardingMissedQuestion from "@components/onboarding/OnboardingMissedQuestion";
import OnboardingProvider from "@components/onboarding/OnboardingProvider";
import OnboardingQuestion from "@components/onboarding/OnboardingQuestions";
import onBoardingSplashScreen from "@components/onboarding/OnboardingSplashScreen";
import OnBoardingSplashScreen from "@components/onboarding/OnboardingSplashScreen";
import OnboardingTopics from "@components/onboarding/OnboardingTopics";
import OnboardingWelcomeSplashScreen from "@components/onboarding/OnboardingWelcomeSplashScreen";
import OnboardingWelcomeUserScreen from "@components/onboarding/OnboardingWelcomeUserScreen";
import RabbleTagAssociation from "@components/onboarding/RabbleTagAssociation";
import usePersistedUser from "@hooks/persistUser";
import useUserVisitedAppAfterDay from "@hooks/useUpdateUserEvents";
import { trpc } from "@providers/RootProvider";
import { TouchProvider } from "@providers/TouchFeedbackProvider";
import {
  DefaultTheme,
  type LinkingOptions,
  NavigationContainer,
  type Route,
  useNavigationContainerRef,
} from "@react-navigation/native";
import OnboardingWalkThroughRabbleScreen from "@screens/OnboardingWalkthrough";
import CreatePostScreen from "@screens/connect/CreatePostScreen";
import PostDetailsScreen from "@screens/connect/PostDetailsScreen";
import ManageHomeScreen from "@screens/manage/ManageHomeScreen";
import ManageStreakScreen from "@screens/manage/ManageStreak";
import ManageTopicListsScreen from "@screens/manage/ManageTopicListsScreen";
import ManageTopicQuestionsList from "@screens/manage/ManageTopicQuestionsList";
import CreateProfileAgeScreen from "@screens/profile/CreateProfileAgeScreen";
import CreateProfileBirthdayScreen from "@screens/profile/CreateProfileBirthdayScreen";
import CreateProfileUserDetailScreen from "@screens/profile/CreateProfileUserDetailScreen";
import NotificationsScreen from "@screens/profile/NotificationScreen";
import CreateOrEditRabbleGroupScreen from "@screens/rabbles/CreateOrEditRabbleGroupScreen";
import RabbleDetailsScreen from "@screens/rabbles/RabbleDetailsScreen";
import SearchRabblesScreen from "@screens/rabbles/SearchRabblesScreen";
import CommunityGuidelines from "@screens/signup/CommunityGuidelines";
import PrivacyPolicy from "@screens/signup/PrivacyPolicy";
import SignupOtpScreenV1 from "@screens/signup/SignupOtpScreenV1";
import SignupScreenV1 from "@screens/signup/SignupScreenV1";
import TermsConditions from "@screens/signup/TermsConditions";
import AsyncStorage from "@utils/asyncStorage";
import mixpanel, { useGlobalActions } from "@utils/mixpanel";
import * as Linking from "expo-linking";
import { useEffect, useRef } from "react";
import type { JwtUser } from "../../../../shared/types/user";
import type { RootNavParams } from "./RootNavParams";

const linking: LinkingOptions<ReactNavigation.RootParamList> = {
  prefixes: [
    Linking.createURL("/"),
    "https://*.getrabble.co",
    "https://getrabble.co",
  ],
  config: {
    screens: {
      RabbleDetailsScreen: "groups/:groupId",
      PostDetailsScreen: "posts/:postId",
      // Handle download path to open the app - will be directed to appropriate screen based on login state
      AppNavigator: {
        screens: {
          LearnNavigator: {
            initialRouteName: "LearnHomeScreen",
            screens: { BlogDetailsScreen: "learn/:blog" },
          },
          ProfileNavigator: "profile",
        },
        path: "download", // This will match /download for logged-in users
      },
      // For non-logged in users, this will be the entry point for /download
      OnBoardingSplashScreen: "download",
    },
  },
  async getInitialURL() {
    // Check if app was opened from a deep link
    const url = await Linking.getInitialURL();
    if (url !== null) {
      const canOpenUrl = await Linking.canOpenURL(url);
      console.log("can open url", canOpenUrl);

      if (!canOpenUrl) return null;

      // Check if the URL contains UTM parameters
      if (url.includes("utm_")) {
        try {
          const urlObj = new URL(url);
          const utmParams: Record<string, string> = {};

          // Extract UTM parameters
          urlObj.searchParams.forEach((value, key) => {
            if (key.startsWith("utm_")) {
              utmParams[key] = value;
            }
          });

          // If we have UTM parameters, track them in Mixpanel
          if (Object.keys(utmParams).length > 0) {
            const user = await AsyncStorage.retrieveData<JwtUser>(
              "persisted-user"
            );

            // Track UTM link open event
            mixpanel.trackEvent(
              "APP_OPEN_FROM_DEEP_LINK",
              {
                ...utmParams,
                url: url,
              },
              user?._id as string,
              "v2"
            );

            // Check if the URL contains download path
            if (url.includes("/download")) {
              try {
                const user = await AsyncStorage.retrieveData<JwtUser>(
                  "persisted-user"
                );

                const isLoggedIn = !!(
                  user?.username ||
                  user?.email ||
                  user?.contact?.phone
                );

                // Track download link open event
                mixpanel.trackEvent(
                  "APP_OPEN_FROM_DOWNLOAD_LINK",
                  {
                    url: url,
                    ...utmParams,
                  },
                  user?._id ? String(user._id) : undefined,
                  "v2"
                );
              } catch (error) {
                console.error("Error tracking download link:", error);
              }
            }
          }
        } catch (error) {
          console.error("Error parsing UTM parameters:", error);
        }
      }

      return url;
    }
  },
};

// If navigation grows to use other navigators use CompositeNavigationProp
// https://reactnavigation.org/docs/typescript/ for reference
export type RootNavigationProp = StackNavigationProp<RootNavParams>;

const Stack = createStackNavigator<RootNavParams>();

const options: StackNavigationOptions = {
  header: NavigationHeader,
  headerLeft: () => (
    <Entypo name="chevron-small-left" size={24} color="black" />
  ),
  headerShown: false,
  cardStyle: { backgroundColor: "#fff" },
};

const theme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    background: "white",
  },
};

export default function RootNavigator() {
  const { persistedUser } = usePersistedUser();
  const navigationRef = useNavigationContainerRef();
  const routeNameRef = useRef<string | undefined>();

  // event listener to track screen views
  useEffect(() => {
    navigationRef.addListener("state", () => {
      const previousRouteName = routeNameRef.current;
      const currentRoute = navigationRef.getCurrentRoute() as
        | Route<string>
        | undefined;

      if (currentRoute) {
        const currentRouteName = currentRoute.name;
        const trackScreenView = async () => {
          const user = await AsyncStorage.retrieveData<JwtUser>(
            "persisted-user"
          );
          mixpanel.trackEvent(
            "SCREEN_TRACKING",
            {
              screen: currentRouteName,
              trackedAt: new Date().toISOString(),
            },
            user?._id as string
          );
        };

        if (previousRouteName !== currentRouteName) {
          // Save the current route name for later comparison
          routeNameRef.current = currentRouteName;

          trackScreenView();
        }
      }
    });
  }, []);

  const globalActions = useGlobalActions();

  // prefetch user profile
  trpc.user.meOrPatientProfile.useQuery();

  // Create event if user visited app after a day (24hrs)
  useUserVisitedAppAfterDay();

  if (persistedUser.isLoading) return null;
  if (
    !persistedUser.data?.username &&
    !persistedUser.data?.email &&
    !persistedUser.data?.contact?.phone
  ) {
    return (
      <NavigationContainer linking={linking} ref={navigationRef} theme={theme}>
        <TouchProvider
          onPress={(data) =>
            globalActions({
              ...data,
              // @ts-ignore
              screenName: navigationRef.getCurrentRoute()?.name,
            })
          }
        >
          <Stack.Navigator
            screenOptions={{ ...options }}
            initialRouteName="OnBoardingSplashScreen"
          >
            <Stack.Screen
              name="OnBoardingSplashScreen"
              component={OnBoardingSplashScreen}
              options={{ gestureEnabled: false }}
            />
            <Stack.Screen
              name="OnBoardingWalkthroughScreen"
              component={OnBoardingWalkthroughScreen}
              options={{ gestureEnabled: false }}
            />
            <Stack.Screen
              name="ManageStreakScreen"
              component={ManageStreakScreen}
              options={{ gestureEnabled: false }}
            />
            <Stack.Screen
              name="OnboardingMissedQuestion"
              component={OnboardingMissedQuestion}
              options={{ gestureEnabled: false }}
            />
            <Stack.Screen
              name="OnboardingDailyLearning"
              component={OnboardingDailyLearning}
              options={{ gestureEnabled: false }}
            />
            <Stack.Screen
              name="OnboardingDailyCheckIn"
              component={OnboardingDailyCheckIn}
              options={{ gestureEnabled: false }}
            />
            <Stack.Screen
              name="OnboardingDailyCheckInQuestion"
              component={OnboardingDailyCheckInQuestion}
              options={{ gestureEnabled: false }}
            />
            <Stack.Screen
              name="OnboardingDailyCheckInProcessed"
              component={OnboardingDailyCheckInProcessed}
              options={{ gestureEnabled: false }}
            />
            <Stack.Screen
              name="OnboardingQuestion"
              component={OnboardingQuestion}
              options={{ gestureEnabled: false }}
            />
            <Stack.Screen
              name="OnboardingBuilding"
              component={OnboardingBuilding}
              options={{ gestureEnabled: false }}
            />
            <Stack.Screen
              name="OnboardingTopics"
              component={OnboardingTopics}
              options={{ gestureEnabled: false }}
            />
            <Stack.Screen
              name="OnboardingGoals"
              component={OnboardingGoals}
              options={{ gestureEnabled: false }}
            />
            <Stack.Screen
              name="OnboardingLastQuestion"
              component={OnboardingLastQuestion}
              options={{ gestureEnabled: false }}
            />
            <Stack.Screen
              name="OnBoardingCreateProfile"
              component={OnBoardingCreateProfile}
              options={{ gestureEnabled: false }}
            />
            <Stack.Screen
              name="OnboardingProvider"
              component={OnboardingProvider}
              options={{ gestureEnabled: false }}
            />
            <Stack.Screen
              name="CreateProfileBirthdayScreen"
              component={CreateProfileBirthdayScreen}
              options={{ gestureEnabled: false }}
            />
            <Stack.Screen
              name="CreateProfileAgeScreen"
              component={CreateProfileAgeScreen}
              options={{ gestureEnabled: false }}
            />
            <Stack.Screen
              name="CreateProfileUserDetailScreen"
              component={CreateProfileUserDetailScreen}
              options={{ gestureEnabled: false }}
            />
            <Stack.Screen
              name="OnboardingLearningQuestion"
              component={OnboardingLearningQuestion}
              options={{ gestureEnabled: false }}
            />
            <Stack.Screen
              name="ManageLearningScreen"
              component={ManageLearningSceen}
              options={{ gestureEnabled: false }}
            />
            <Stack.Screen
              name="CreateNewRabble"
              component={CreateNewRabble}
              options={{ gestureEnabled: false }}
            />
            <Stack.Screen
              name="GroupPrivacyPreferences"
              component={GroupPrivacyPreferences}
              options={{ gestureEnabled: false }}
            />
            <Stack.Screen
              name="RabbleTagAssociation"
              component={RabbleTagAssociation}
              options={{ gestureEnabled: false }}
            />
            <Stack.Screen
              name="OnboardingContacts"
              component={OnboardingContacts}
              options={{ gestureEnabled: false }}
            />
            <Stack.Screen
              name="OnboardingWelcomeSplashScreen"
              component={OnboardingWelcomeSplashScreen}
              options={{ gestureEnabled: false }}
            />
            <Stack.Screen
              name="OnboardingContactList"
              component={OnboardingContactList}
              options={{ gestureEnabled: false }}
            />
            <Stack.Screen
              name="SignupScreenV1"
              component={SignupScreenV1}
              options={{ gestureEnabled: false }}
            />
            <Stack.Screen
              name="SignupOtpScreenV1"
              component={SignupOtpScreenV1}
              options={{ gestureEnabled: false }}
            />
            <Stack.Screen
              name="WalkthroughScreen"
              component={WalkThroughScreen}
            />
            <Stack.Screen
              name="OnboardingWalkThroughRabbleScreen"
              component={OnboardingWalkThroughRabbleScreen}
            />
            <Stack.Screen
              name="OnboardingWelcomeUserScreen"
              component={OnboardingWelcomeUserScreen}
              options={{ gestureEnabled: false }}
            />
            <Stack.Screen
              name="LoginSplashScreen"
              component={LoginSplashScreen}
              options={{
                headerShown: true,
                headerTitle: "",
              }}
            />
            <Stack.Screen
              name="LoginMobileScreen"
              component={LoginMobileScreen}
              options={{
                headerShown: true,
                headerTitle: "Log In",
              }}
            />
            <Stack.Screen
              name="LoginOtpScreen"
              component={LoginOtpScreen}
              options={{
                headerShown: true,
                headerTitle: "",
              }}
            />
            <Stack.Screen
              name="SignupScreen"
              component={SignupScreen}
              options={{
                headerShown: true,
                headerTitle: "sign up",
              }}
            />

            <Stack.Screen
              name="PrivacyPolicy"
              component={PrivacyPolicy}
              options={{
                headerShown: true,
                headerTitle: "Privacy Policy",
                presentation: "modal",
              }}
            />
            <Stack.Screen
              name="TermsConditions"
              component={TermsConditions}
              options={{
                headerShown: true,
                headerTitle: "Terms & Conditions",
                presentation: "modal",
              }}
            />
            <Stack.Screen
              name="CommunityGuidelines"
              component={CommunityGuidelines}
              options={{
                headerShown: true,
                headerTitle: "Community Guidelines",
                presentation: "modal",
              }}
            />
          </Stack.Navigator>
        </TouchProvider>
      </NavigationContainer>
    );
  } else {
    return (
      <NavigationContainer linking={linking} ref={navigationRef} theme={theme}>
        <TouchProvider
          onPress={(data) =>
            globalActions({
              ...data,
              // @ts-ignore
              screenName: navigationRef.getCurrentRoute()?.name,
            })
          }
        >
          <Stack.Navigator screenOptions={{ ...options }}>
            {/* Home */}
            <Stack.Screen name="AppNavigator" component={AppNavigator} />

            {/* Login */}
            <Stack.Screen
              name="PrivacyPolicy"
              component={PrivacyPolicy}
              options={{
                headerShown: true,
                headerTitle: "Privacy Policy",
                presentation: "modal",
              }}
            />
            <Stack.Screen
              name="TermsConditions"
              component={TermsConditions}
              options={{
                headerShown: true,
                headerTitle: "Terms & Conditions",
                presentation: "modal",
              }}
            />
            <Stack.Screen
              name="CommunityGuidelines"
              component={CommunityGuidelines}
              options={{
                headerShown: true,
                headerTitle: "Community Guidelines",
                presentation: "modal",
              }}
            />

            {/* Connect */}
            <Stack.Screen
              name="CreatePostScreen"
              component={CreatePostScreen}
              options={{ headerShown: true, headerTitle: "create post" }}
            />
            <Stack.Screen
              name="PostDetailsScreen"
              component={PostDetailsScreen}
              options={{ headerShown: true, headerTitle: "" }}
            />

            {/* Rabble Groups */}
            <Stack.Screen
              name="CreateOrEditRabbleGroupScreen"
              component={CreateOrEditRabbleGroupScreen}
              options={({ route }) => ({
                headerShown: true,
                headerTitle: route.params?.groupId
                  ? "edit group"
                  : "create group",
              })}
            />
            <Stack.Screen
              name="RabbleDetailsScreen"
              component={RabbleDetailsScreen}
              options={{ headerShown: true, headerTitle: "my rabbles" }}
            />
            <Stack.Screen
              name="SearchRabblesScreen"
              component={SearchRabblesScreen}
              options={{ headerShown: true, headerTitle: "search groups" }}
            />
            <Stack.Screen
              name="NotificationScreen"
              component={NotificationsScreen}
              options={{ headerShown: true, headerTitle: "notifications" }}
            />

            <Stack.Screen
              name="TopicQuestionsList"
              component={ManageTopicQuestionsList}
              options={{ headerShown: false }}
            />
            <Stack.Screen
              name="TopicsListScreen"
              component={ManageTopicListsScreen}
              options={{ headerShown: false }}
            />
          </Stack.Navigator>
        </TouchProvider>
      </NavigationContainer>
    );
  }
}
