import { PostTypes } from "@models/Posts";
import { RabbleGroup as RabbleGroupDetails } from "../../../../shared/types/RabbleGroup";
import { RabbleGroup, RabbleGroupUserRole } from "@models/index";

type Contact = { id: string; name: string; phoneNumbers: string, email: string };

export type RootNavParams = {
  WalkthroughScreen: undefined;
  OnBoardingWalkthroughScreen: undefined;
  OnBoardingSplashScreen: undefined;
  OnboardingQuestion: {
    topicId?: any;
    currentIndex?: number;
    questionAnswer?: any;
    fromManageScreen?: boolean | any;
  };
  OnboardingBuilding: undefined;
  OnboardingTopics?: { fromManageScreen?: boolean | any };
  OnboardingGoals: {
    topicId?: string;
    currentIndex?: number;
    questionAnswer?: any;
  };
  OnboardingLastQuestion: {
    topicId?: string;
    currentIndex?: number;
    questionAnswer?: any;
  };
  OnBoardingCreateProfile: { user?: any | undefined };
  OnboardingProvider: { fromManageScreen?: boolean | any };
  CreateProfileBirthdayScreen: undefined;
  CreateProfileAgeScreen: undefined;
  CreateProfileUserDetailScreen: undefined;
  OnboardingLearningQuestion: undefined;
  OnboardingMissedQuestion: undefined;
  OnboardingDailyCheckIn: undefined;
  OnboardingDailyLearning: undefined;
  ManageStreakScreen?: {
    streakCount?: number | string;
    dailyTrackers?: any;
    isDailyCheckInCompleted?: any;
  };
  OnboardingDailyCheckInQuestion: undefined;
  OnboardingDailyCheckInProcessed: undefined;
  OnboardingContacts: { groupId?: string, groupTags?: any };
  OnboardingContactList: { contacts?: Contact | Contact[], groupId?: string };
  OnboardingWalkThroughRabbleScreen: undefined;
  OnboardingWelcomeSplashScreen: undefined;
  OnboardingWelcomeUserScreen:  { groupId?: string, invitedContacts?: any };
  ManageLearningScreen: { topicId?: string, source?: string };
  CreateNewRabble: undefined;
  GroupPrivacyPreferences: { groupId?: any | undefined };
  RabbleTagAssociation: { groupId?: any | undefined };

  // Login
  LoginSplashScreen: undefined;
  LoginMobileScreen?: {
    presentation?: "modal";
    signupType?: "caregiver";
    hideSkipBtn?: boolean;
  };
  // LoginEmailScreen?: { presentation?: "modal" };
  LoginOtpScreen: {
    phoneOrEmail: string;
    type: "phone" | "email";
    deviceId: string | null | undefined;
  };

  // Signup
  SignupScreen?: { phoneOrEmailData: string };
  SignupScreenV1: undefined;
  SignupOtpScreenV1: {
    phoneOrEmail: string;
    deviceId: string | null | undefined;
  };
  MobileSignupScreen: { firstname: string; lastname: string; userName: string };
  SignupOtpScreen: {
    firstname: string;
    lastname: string;
    userName: string;
    phoneOrEmail: string;
    type: "phone" | "email";
    marketingConsentAccepted: boolean;
  };
  EmailSignupScreen: { firstname: string; lastname: string; userName: string };
  TermsConditions: undefined;
  CommunityGuidelines: undefined;
  PrivacyPolicy: undefined;

  // Profile
  AppNavigator: undefined;

  // Connect
  CreatePostScreen: {
    postType: PostTypes;
    rabbleGroup?: string;
    connectPost?: any;
  };
  PostDetailsScreen: {
    postId: string;
    postType: "CONNECT" | "PRIVATE" | "PUBLIC";
    groupId?: string;
    groupRole?: RabbleGroupUserRole;
  };

  // Rabbles
  CreateOrEditRabbleGroupScreen?: {
    rabbleGroupInfo?: RabbleGroupDetails;
    groupId?: string;
    diseaseTags?: string[];
    tags?: string[];
  };
  RabbleDetailsScreen: RabbleGroup;
  SearchRabblesScreen: undefined;
  NotificationScreen: undefined;

  // Manage
  TopicQuestionsList: { topicId: string };
  TopicsListScreen: undefined;
  ManageHomeScreen: undefined;
};
