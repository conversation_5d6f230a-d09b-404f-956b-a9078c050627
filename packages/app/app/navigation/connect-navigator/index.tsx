import NavigationHeader from "@components/navigation/NavigationHeader";
import { Entypo } from "@expo/vector-icons";
import {
  createStackNavigator,
  StackNavigationOptions,
} from "@react-navigation/stack";

import Connect from "@screens/connect/ConnectPosts";
import {
  ConnectNavParams,
  ConnectPostNavParams,
  RabbleGroupsNavParams,
} from "./ConnectNavParams";
import { createMaterialTopTabNavigator } from "@react-navigation/material-top-tabs";
import RabbleGroups from "@screens/rabbles/RabbleGroups";
import { SafeAreaView } from "react-native";
import Screen from "@components/common/Screen";

const options: StackNavigationOptions = {
  header: NavigationHeader,
  headerLeft: () => (
    <Entypo name="chevron-small-left" size={24} color="black" />
  ),
  headerShown: false,
  cardStyle: { backgroundColor: "#fff" },
};

const ConnectStack = createStackNavigator<ConnectPostNavParams>();
function ConnectPostsNav() {
  return (
    <ConnectStack.Navigator screenOptions={{ ...options }}>
      <ConnectStack.Screen name="ConnectScreen" component={Connect} />
    </ConnectStack.Navigator>
  );
}

const RabbleGroupStack = createStackNavigator<RabbleGroupsNavParams>();
function RabbleGroupsNav() {
  return (
    <RabbleGroupStack.Navigator screenOptions={{ ...options }}>
      <RabbleGroupStack.Screen
        name="RabbleGroupsScreen"
        component={RabbleGroups}
      />
    </RabbleGroupStack.Navigator>
  );
}

const Tab = createMaterialTopTabNavigator<ConnectNavParams>();
export default function ConnectNavigator() {
  return (
    <Screen includeHeader headerClasses="mx-4">
      <Tab.Navigator
        screenOptions={{
          tabBarActiveTintColor: "#FF8E1C",
          tabBarAndroidRipple: { color: "#FF8E1C50", borderless: true },
          tabBarIndicatorStyle: { backgroundColor: "#FF8E1C" },
          tabBarStyle: { elevation: 0 },
          tabBarLabelStyle: {
            fontFamily: "Montserrat_500Medium",
            textTransform: "none",
            fontSize: 16,
          },
        }}
      >
        <Tab.Screen
          name="ConnectPosts"
          component={ConnectPostsNav}
          options={{
            tabBarLabel: "Posts",
          }}
        />
        <Tab.Screen
          name="RabbleGroups"
          component={RabbleGroupsNav}
          options={{
            tabBarLabel: "Rabbles",
          }}
        />
      </Tab.Navigator>
    </Screen>
  );
}
