{"expo": {"name": "myRabble", "slug": "rabble", "scheme": "myrabble", "version": "2.3.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "plugins": [["react-native-fbsdk-next", {"appID": "2401184496898171", "displayName": "rabbleHealth", "clientToken": "4d9c9c36595942b87e0c455e5f36907b", "scheme": "myrabble", "advertiserIDCollectionEnabled": true, "autoLogAppEventsEnabled": true, "isAutoInitEnabled": true, "iosUserTrackingPermission": "Allow this app to collect app-related data that can be used for tracking you or your device."}], ["expo-splash-screen", {"resizeMode": "contain", "backgroundColor": "#004987", "image": "./assets/splash-icon.png", "imageWidth": 200}], ["expo-image-picker", {"photosPermission": "The app accesses your photos to let you share them with your friends and connect"}], ["expo-contacts", {"contactsPermission": "Allow app to access your contacts."}], ["onesignal-expo-plugin", {"mode": "development"}], "sentry-expo", "expo-font", "expo-tracking-transparency"], "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.rabblehealth.app", "buildNumber": "283", "associatedDomains": ["applinks:getrabble.co", "applinks:dev-admin.getrabble.co"], "infoPlist": {"NSPhotoLibraryUsageDescription": "The app accesses your photos to let you share them with your friends and connect", "NSLocationWhenInUseUsageDescription": "The app accesses your location to let you share it with your friends and connect", "NSUserTrackingUsageDescription": "Allow this app to collect app-related data that can be used for tracking you or your device.", "SKAdNetworkItems": [{"SKAdNetworkIdentifier": "v9wttpbfk9.skadnetwork"}, {"SKAdNetworkIdentifier": "n38lu8286q.skadnetwork"}, {"SKAdNetworkIdentifier": "v9wttpbfk9.skadnetwork"}, {"SKAdNetworkIdentifier": "n38lu8286q.skadnetwork"}], "ITSAppUsesNonExemptEncryption": false}}, "android": {"intentFilters": [{"action": "VIEW", "autoVerify": true, "data": [{"scheme": "https", "host": "getrabble.co", "pathPrefix": "/groups"}, {"scheme": "https", "host": "dev-admin.getrabble.co", "pathPrefix": "/groups"}, {"scheme": "https", "host": "getrabble.co", "pathPrefix": "/learn"}, {"scheme": "https", "host": "dev-admin.getrabble.co", "pathPrefix": "/learn"}, {"scheme": "https", "host": "getrabble.co", "pathPrefix": "/profile"}, {"scheme": "https", "host": "dev-admin.getrabble.co", "pathPrefix": "/profile"}], "category": ["BROWSABLE", "DEFAULT"]}], "adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#004987"}, "package": "com.rabblehealth.app", "versionCode": 283, "permissions": ["android.permission.INTERNET", "android.permission.RECORD_AUDIO", "com.google.android.gms.permission.AD_ID"]}, "extra": {"oneSignalAppId": "************************************", "eas": {"projectId": "5e7b7dcb-a464-4350-be7b-3d89f874fd7b"}}, "runtimeVersion": {"policy": "sdkVersion"}, "updates": {"url": "https://u.expo.dev/5e7b7dcb-a464-4350-be7b-3d89f874fd7b"}}}