import { z } from "zod";
import { ServiceStatusEnum } from "../types/service";

export const createCategoryValidator = z.object({
  title: z.string().min(1, "this is a required field"),
  logo: z.string().min(1, "this is a required field"),
  description: z.string().optional(),
});

export const createOrUpdateServiceValidator = z.object({
  _id: z.string().optional(),
  category: z.array(z.string().min(1, "this is a required field")),
  states: z.array(z.string().min(1, "this is a required field")).min(1),
  logo: z.string().min(1, "this is a required field"),
  organization: z.string().min(1, "this is a required field"),
  title: z.string().min(1, "this is a required field"),
  description: z.string().min(1, "this is a required field"),
  phone: z.string().optional(),
  email: z.string().email().optional().or(z.literal("").optional()),
  website: z.string().optional(),
  tags: z.array(z.string()),
  status: z
    .string()
    .refine((value) => value !== "", {
      message: "Service status is required",
    })
    .refine(
      (value) =>
        Object.values(ServiceStatusEnum).includes(value as ServiceStatusEnum),
      {
        message:
          "Invalid service status. Allowed values are: ACTIVE, INACTIVE.",
      }
    ),
});

export const updateServiceSequenceValidator = z.array(
  z.object({ serviceId: z.string(), sequence: z.coerce.number() })
);

export const serviceFilterValidator = z
  .object({
    states: z.array(z.string()).optional(),
    category: z.string().optional(),
    tags: z.array(z.string()).optional(),
    status: z.array(z.nativeEnum(ServiceStatusEnum).optional()).optional(),
  })
  .optional();
