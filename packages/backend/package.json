{"name": "rabble_be", "version": "1.0.0", "description": "", "main": "app.ts", "private": true, "scripts": {"build": "npx tsc", "format": "prettier --write .", "lint": "eslint --fix --max-warnings=1 .", "start": "node dist/backend/app.js", "dev": "nodemon --files", "prod": "nodemon --files --config nodemon.prod.json"}, "author": "", "license": "ISC", "devDependencies": {"@tsconfig/recommended": "^1.0.1", "@types/bcryptjs": "^2.4.2", "@types/cors": "^2.8.13", "@types/csvtojson": "^2.0.3", "@types/express": "^4.17.14", "@types/express-fileupload": "^1.4.1", "@types/http-errors": "^2.0.1", "@types/jsonwebtoken": "^8.5.9", "@types/lodash": "^4.14.191", "@types/memoizee": "^0.4.11", "@types/mime-types": "^2.1.1", "@types/node": "^18.11.9", "@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.7", "@typescript-eslint/eslint-plugin": "^5.44.0", "@typescript-eslint/parser": "^5.44.0", "eslint": "^8.28.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^8.5.0", "eslint-config-standard-with-typescript": "^23.0.0", "eslint-import-resolver-typescript": "^3.5.2", "eslint-plugin-import": "^2.25.2", "eslint-plugin-n": "^15.0.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-promise": "^6.0.0", "prettier": "^2.8.0", "ts-node": "^10.9.2", "typescript": "^5.3.3"}, "dependencies": {"@aws-sdk/client-pinpoint": "^3.540.0", "@aws-sdk/client-s3": "^3.287.0", "@aws-sdk/s3-request-presigner": "^3.478.0", "@trpc/server": "^10.44.1", "accesscontrol": "^2.2.1", "aws-sdk": "^2.1331.0", "axios": "^1.5.1", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "csvtojson": "^2.0.10", "date-fns": "^2.30.0", "date-fns-tz": "^2.0.0", "express": "^4.18.2", "express-async-errors": "^3.1.1", "express-fileupload": "^1.4.0", "form-data": "^4.0.0", "http-errors": "^2.0.0", "joi": "^17.7.0", "jsonwebtoken": "^8.5.1", "lodash": "^4.17.21", "memoizee": "^0.4.17", "mime-types": "^2.1.35", "moment": "^2.29.4", "moment-timezone": "^0.5.40", "mongoose": "^7.0.1", "mongoose-paginate-v2": "^1.8.0", "nanoid": "^3.0.0", "node-cron": "^3.0.3", "nodemailer": "^6.9.3", "nodemon": "^2.0.21", "quickchart-js": "^3.1.3", "superjson": "1.13.3", "trpc-panel": "^1.3.4", "twilio": "^4.20.0", "xlsx": "^0.18.5", "zod": "^3.22.4"}}