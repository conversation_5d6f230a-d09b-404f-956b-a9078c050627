import { Types } from "mongoose";
import { z } from "zod";
import {
  createCategoryValidator,
  createOrUpdateServiceValidator,
  updateServiceSequenceValidator,
} from "../../../shared/validators/service.validator";
import { ServiceCategoryModel, ServiceModel } from "../models/Service";

export const createCategory = (
  payload: z.infer<typeof createCategoryValidator>
) => new ServiceCategoryModel(payload).save();
export const createUpdateService = async ({
  _id,
  ...payload
}: z.infer<typeof createOrUpdateServiceValidator>) => {
  // Process the payload
  const processedPayload = { ...payload };

  // Handle tags - ensure we're using ObjectIds
  if (payload.tags && payload.tags.length > 0) {
    // Filter out valid ObjectId strings and convert them to ObjectIds
    const validTags: any[] = [];
    for (const tagId of payload.tags) {
      // Check if it's a valid ObjectId string
      if (typeof tagId === "string" && /^[0-9a-fA-F]{24}$/.test(tagId)) {
        try {
          validTags.push(new Types.ObjectId(tagId));
        } catch (err) {
          console.error(`Failed to convert ${tagId} to ObjectId:`, err);
        }
      } else if (typeof tagId !== "string") {
        // If it's already an ObjectId, just use it
        validTags.push(tagId);
      }
      // Skip strings that aren't valid ObjectIds
    }

    (processedPayload as any).tags = validTags;
  }

  // Create or update the service
  if (!_id) {
    await ServiceModel.create({
      ...processedPayload,
      // @ts-ignore
      category: [...new Set(processedPayload.category)],
    });
  } else {
    await ServiceModel.findOneAndUpdate(
      { _id },
      {
        ...processedPayload,
        // @ts-ignore
        category: [...new Set(processedPayload.category)],
      },
      { upsert: true }
    );
  }

  return { status: "OK" };
};

export const updateServiceSequence = async (
  payload: z.infer<typeof updateServiceSequenceValidator>
) => {
  const bulkOperation = payload.map((data) => ({
    updateOne: {
      filter: { _id: new Types.ObjectId(data.serviceId) },
      update: { $set: { sequence: data.sequence } },
    },
  }));

  await ServiceModel.bulkWrite(bulkOperation);

  return { status: "OK" };
};
