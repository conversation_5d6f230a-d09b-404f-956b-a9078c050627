import { z } from "zod";
import { FilterQuery } from "mongoose";
import {
  createBlogTagValidator,
  manageBlogValidator,
  getBlogsValidator,
  updateBlogTagValidator,
  manageBlogsDashboardValidator,
  getBlogValidator,
  getTagsValidator,
} from "../../../shared/validators/blog";
import { BlogDashboardModel, BlogModel, BlogTagModel } from "../models/Blog";
import { Blog } from "../../../shared/types/Blog";
import { TRPCError } from "@trpc/server";

export const getTags = (filter?: z.infer<typeof getTagsValidator>) => {
  let query: FilterQuery<any> = {};

  if (filter?.scope?.length) {
    // Match tags that contain ANY of the selected scope values (OR logic)
    query.scope = { $in: filter.scope };
  }

  return BlogTagModel.find(query).lean();
};
export const deleteBlogTag = async (blogTagId: string) => {
  const blogTag = await BlogTagModel.exists({ _id: blogTagId });
  if (!blogTag) throw new TRPCError({ code: "NOT_FOUND" });

  await BlogTagModel.findByIdAndDelete(blogTagId);
  await BlogModel.updateMany({}, { $pull: { tags: blogTagId } });

  return { status: "ok" };
};
export const createBlogTag = (tag: z.infer<typeof createBlogTagValidator>) =>
  new BlogTagModel(tag).save();
export const updateBlogTag = ({
  tag,
  scope,
  tagId,
}: z.infer<typeof updateBlogTagValidator>) => {
  // Build update object with only provided fields
  const updateData: any = {};
  if (tag !== undefined) updateData.tag = tag;
  if (scope !== undefined) updateData.scope = scope;

  return BlogTagModel.findByIdAndUpdate(tagId, updateData, { new: true });
};

export const manageBlog = async (blog: z.infer<typeof manageBlogValidator>) => {
  const { blogId, diseaseTags, ...rest } = blog;

  // If diseaseTags is provided, merge it with tags for backward compatibility
  if (diseaseTags && diseaseTags.length > 0) {
    rest.tags = [...(rest.tags || []), ...diseaseTags];
  }

  if (blogId) await BlogModel.findByIdAndUpdate(blogId, { ...rest });
  else new BlogModel(rest).save();
};

export const getBlogs = (filter: z.infer<typeof getBlogsValidator>) => {
  let query: FilterQuery<Blog> = {};
  if (filter) {
    const { tags, diseaseTags, ...other } = filter;

    // Combine tags and diseaseTags for backward compatibility
    const allTags = [...(tags || []), ...(diseaseTags || [])];

    query = {
      ...other,
      ...(allTags.length > 0 && { tags: { $in: allTags } }),
    };
  }

  return BlogModel.find(query)
    .populate<{ tags?: { _id: string; tag: string; scope?: string[] }[] }>([
      { path: "tags" },
    ])
    .sort({ createdAt: -1 })
    .lean();
};

export const getBlog = ({ blog }: z.infer<typeof getBlogValidator>) =>
  BlogModel.findById(blog);

export const manageBlogDashboard = async (
  payload: z.infer<typeof manageBlogsDashboardValidator>
) => BlogDashboardModel.updateOne({}, payload, { upsert: true });
