// Import necessary components and utilities
import { FormComboBoxPopover } from "@web/components/form/FormComboPopover";
import <PERSON><PERSON>ield from "@web/components/form/FormField";
import S3FileUpload from "@web/components/form/S3FileUpload";
import { FormTextField } from "@web/components/form/FormTextField";
import { Button } from "@web/components/ui/button";
import { Label } from "@web/components/ui/label";
import { ScrollArea } from "@web/components/ui/scroll-area";
import { trpc } from "@web/providers/Providers";
import { createOrUpdateServiceValidator } from "packages/shared/validators/service.validator";
import { FormProvider, UseFormReturn } from "react-hook-form";
import { z } from "zod";
import { toastPromise, imgUrl } from "@web/lib/utils";
import { FormSelect } from "@web/components/form/FormSelect";
import { ServiceStatusEnum } from "packages/shared/types/service";
import { useEffect, useState } from "react";

type Form = z.infer<typeof createOrUpdateServiceValidator>;
type Props = {
  methods: UseFormReturn<Form>;
};

export default function CreateService({ methods }: Props) {
  const { data: categories } = trpc.service.getCategories.useQuery();
  const { data: states } = trpc.lib.states.useQuery();
  const { data: blogTags } = trpc.blog.tags.useQuery();
  const [logoPreview, setLogoPreview] = useState<string | null>(null);

  const serviceId = methods.watch("_id");
  const logo = methods.watch("logo");

  trpc.service.getServiceById.useQuery(
    // @ts-expect-error
    serviceId,
    {
      enabled: !!serviceId,
      onSuccess: (data: any) => {
        Object.entries(data).forEach(([key, value]) => {
          // @ts-expect-error
          methods.setValue(key, value);
        });
      },
    }
  );

  const createUpdateService = trpc.service.createUpdateService.useMutation();

  // Update logo preview when logo changes
  useEffect(() => {
    if (logo) {
      setLogoPreview(logo);
    }
  }, [logo]);

  const handleSubmit = methods.handleSubmit((data) =>
    toastPromise({
      asyncFunc: createUpdateService.mutateAsync(data),
      success: "services created successfully",
    })
  );

  return (
    <ScrollArea>
      <form className="m-3" onSubmit={handleSubmit}>
        <FormProvider {...methods}>
          <Label>Create Services</Label>
          <div className="mt-2 flex flex-col gap-4">
            <FormField
              name="title"
              label="Service Name"
              placeholder="Enter service name"
            />
            <FormTextField
              name="description"
              label="Enter Service Description"
              placeholder="Enter service description"
            />
            <FormSelect
              name="status"
              label="Service status"
              placeholder="Select service status"
              options={[
                { label: "Active", value: ServiceStatusEnum.ACTIVE },
                { label: "In-active", value: ServiceStatusEnum.INACTIVE },
              ]}
            />
            <FormComboBoxPopover
              name="category"
              label="Categories"
              placeholder="Select Categories"
              // @ts-ignore
              options={
                categories?.map((category) => ({
                  label: category.title,
                  value: category._id,
                })) || []
              }
            />
            <FormComboBoxPopover
              name="tags"
              label="Tags"
              placeholder="Select Tags"
              // @ts-ignore
              options={
                blogTags?.map((tag) => ({
                  label: tag.tag,
                  value: tag._id.toString(),
                })) || []
              }
            />
            <FormField
              name="email"
              label="Email"
              placeholder="Enter service email"
              type="email"
            />
            <FormField
              name="organization"
              label="Organization"
              placeholder="Enter service organization"
            />
            <FormField
              name="phone"
              label="Contact"
              placeholder="Enter service contact"
              type="tel"
            />
            <FormComboBoxPopover
              name="states"
              label="State"
              placeholder="Select State"
              // @ts-ignore
              options={[
                { label: "National", value: "National" },
                ...(states?.map((state) => ({
                  label: state,
                  value: state,
                })) || []),
              ]}
            />
            <FormField
              name="website"
              label="Service Website"
              placeholder="Enter service website"
            />
            <div className="space-y-2">
              <Label>Service Logo</Label>
              <div className="flex items-center gap-4">
                <S3FileUpload name="logo" path="services" prefix="service" />
                {logoPreview && (
                  <div className="relative">
                    <img
                      src={
                        logoPreview.includes("http")
                          ? logoPreview
                          : imgUrl(logoPreview)
                      }
                      alt="Service Logo"
                      className="w-16 h-16 object-contain border rounded"
                    />
                    <div className="text-xs text-muted-foreground mt-1">
                      Current Logo
                    </div>
                  </div>
                )}
              </div>
            </div>
            <Button>{serviceId ? "Update Service" : "Create Service"}</Button>
          </div>
        </FormProvider>
      </form>
    </ScrollArea>
  );
}
