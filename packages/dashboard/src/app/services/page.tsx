"use client";

import { <PERSON>rollA<PERSON> } from "@web/components/ui/scroll-area";
import {
  Table,
  TableBody,
  TableCell,
  TableHeader,
  TableRow,
} from "@web/components/ui/table";
import * as z from "zod";
import CreateServiceDialog from "@web/elements/CreateService.Dialog";
import { trpc } from "@web/providers/Providers";
import { createOrUpdateServiceValidator } from "packages/shared/validators/service.validator";
import { zodResolver } from "@hookform/resolvers/zod";
import { PlusIcon } from "lucide-react";
import { useState, useMemo } from "react";
import { useForm } from "react-hook-form";
import { Button } from "@web/components/ui/button";
import { ServiceStatusEnum } from "packages/shared/types/service";
import { Reorder } from "framer-motion";
import Image from "next/image";
import _ from "lodash";
import useDebounce from "packages/dashboard/hooks/useDebounce";
import { toastPromise } from "@web/lib/utils";
import { Badge } from "@web/components/ui/badge";

type Form = z.infer<typeof createOrUpdateServiceValidator>;

type ServiceItem = {
  _id: string;
  title: string;
  description: string;
  logo: string;
  status: ServiceStatusEnum;
  sequence?: number;
  topics?: string[];
};

export default function Services() {
  const [showDialog, setShowDialog] = useState(false);
  const [orderedItems, setOrderedItems] = useState<ServiceItem[]>([]);

  const { mutateAsync: updateServiceSequence } =
    trpc.service.updateServiceSequence.useMutation();

  useDebounce(orderedItems, 500, (data) => {
    // toastPromise({
    //   asyncFunc: updateServiceSequence(
    //     data.map((d, idx) => ({ serviceId: String(d._id), sequence: idx }))
    //   ),
    //   success: "sequence updated",
    // });
  });

  // Fetch all tags to map tag IDs to names
  const { data: allTags } = trpc.blog.tags.useQuery();

  // Create a map of tag IDs to tag names
  const tagIdToNameMap = useMemo(() => {
    const map = new Map();
    if (allTags) {
      allTags.forEach((tag) => {
        map.set(tag._id.toString(), tag.tag);
      });
    }
    return map;
  }, [allTags]);

  const services = trpc.service.getService.useQuery(
    {
      status: [ServiceStatusEnum.ACTIVE, ServiceStatusEnum.INACTIVE],
    },
    {
      onSuccess(data) {
        setOrderedItems(
          data.map((d) => ({
            _id: String(d._id),
            title: d.title,
            description: d.description,
            logo: d.logo,
            status: d.status,
            sequence: d.sequence ?? 0,
            // Handle tags (objects with _id and tag)
            tags: (d.tags || []).map((tag) => {
              if (typeof tag === "string") {
                return tagIdToNameMap.get(tag) || tag; // String ID
              } else if (tag._id) {
                // Populated tag object
                return (
                  // @ts-ignore
                  tag.tag ||
                  tagIdToNameMap.get(tag._id.toString()) ||
                  String(tag._id)
                );
              }
              return String(tag); // Fallback
            }),
          }))
        );
      },
    }
  );

  const methods = useForm<Form>({
    resolver: zodResolver(createOrUpdateServiceValidator),
    defaultValues: {
      _id: "",
      category: [],
      description: "",
      email: "",
      logo: "",
      organization: "",
      phone: "",
      states: [],
      title: "",
      website: "",
      tags: [],
      status: "" as unknown as ServiceStatusEnum,
    },
  });

  return (
    <div className="p-4">
      <div className="flex justify-end mb-4">
        <CreateServiceDialog
          open={showDialog}
          onOpenChange={(data) => {
            setShowDialog(data);
            if (!data) {
              methods.reset();
            }
          }}
          methods={methods}
        />
        <Button
          onClick={() => {
            methods.setValue("_id", "");
            setShowDialog(true);
          }}
        >
          <PlusIcon className="w-4 h-4 mr-2" /> Create Services
        </Button>
      </div>

      <ScrollArea className="h-[80vh]">
        <Table>
          <TableHeader>
            <TableRow className="font-semibold">
              <TableCell>Logo</TableCell>
              <TableCell>Service</TableCell>
              <TableCell>Tags</TableCell>
              <TableCell>Description</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Action</TableCell>
            </TableRow>
          </TableHeader>
        </Table>

        <Reorder.Group
          axis="y"
          values={orderedItems}
          onReorder={(items) => {
            setOrderedItems(items);
            console.log(items.map((_, idx) => `${_.title}::${idx}`));

            toastPromise({
              asyncFunc: updateServiceSequence(
                items.map((i, idx) => ({
                  serviceId: String(i._id),
                  sequence: idx,
                }))
              ),
              success: "sequence updated",
            });
          }}
          className="w-full"
        >
          {orderedItems.map((item) => (
            <Reorder.Item
              key={item._id}
              value={item}
              className="flex w-full border-b border-gray-200 hover:bg-gray-50 cursor-move"
            >
              <div className="flex w-full py-4">
                <div className="w-[250px] px-4">
                  <Image
                    width={50}
                    height={50}
                    src={item.logo}
                    alt=""
                    className="h-[50px] w-[50px] object-contain"
                  />
                </div>
                <div className="w-1/6 px-4">{item.title}</div>
                <div className="w-1/6 px-4">
                  {/* @ts-ignore */}
                  {item.tags?.map((t, index) => (
                    <Badge key={index} className="mr-1 mb-1">
                      {t}
                    </Badge>
                  ))}
                </div>
                <div className="w-1/6 px-4">
                  <span className="line-clamp-2">{item.description}</span>
                </div>
                <div className="w-1/6 px-4">
                  <span className="line-clamp-2">{item.status}</span>
                </div>
                <div className="w-1/6 px-4">
                  <Button
                    onClick={() => {
                      methods.setValue("_id", item._id);
                      setShowDialog(true);
                    }}
                  >
                    Update
                  </Button>
                </div>
              </div>
            </Reorder.Item>
          ))}
        </Reorder.Group>
      </ScrollArea>
    </div>
  );
}
