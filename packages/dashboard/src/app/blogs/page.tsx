"use client";
import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  AlertDialog<PERSON>ontent,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@web/components/ui/alert-dialog";
import { Badge } from "@web/components/ui/badge";
import { Button } from "@web/components/ui/button";
import { Checkbox } from "@web/components/ui/checkbox";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@web/components/ui/table";
import ManageBlogPostForm from "@web/elements/form/ManageBlogPost";
import { toastPromise } from "@web/lib/utils";
import { trpc } from "@web/providers/Providers";
import { format } from "date-fns";
import {
  Edit,
  Trash,
  X,
  ChevronUp,
  ChevronDown,
  ChevronsUpDown,
  Tags,
} from "lucide-react";
import { FormMultiSelect } from "@web/components/form/FormMultiSelect";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, useForm } from "react-hook-form";
import { z } from "zod";
import { RouterOutput } from "../../../../shared";
import { BlogStatus } from "../../../../shared/types/Blog";
import { manageBlogValidator } from "../../../../shared/validators/blog";

type BlogForm = z.infer<typeof manageBlogValidator>;

export default function UpdateBlog() {
  const [open, setOpen] = useState(false);

  const blogs = trpc.blog.getBlogs.useQuery();
  const blogTags = trpc.blog.tags.useQuery();

  const methods = useForm<BlogForm>({
    resolver: zodResolver(manageBlogValidator),
    defaultValues: {
      author: "",
      banner: "",
      blogId: "",
      contentHtml: "",
      description: "",
      status: BlogStatus.DRAFT,
      tags: [],
      timeToReadInMins: "" as unknown as number,
      title: "",
      editorData: [],
    },
  });

  const createBlog = trpc.blog.createBlog.useMutation();
  const deleteBlog = trpc.blog.deleteBlog.useMutation();
  const router = useRouter();

  const handleSubmit: SubmitHandler<BlogForm> = (data) => {
    // Log tag IDs instead of tag names
    const selectedTagIds = data.tags.map((t) =>
      blogTags.data?.find((tag) => tag.tag === t)?._id.toString()
    );

    toastPromise({
      asyncFunc: createBlog.mutateAsync({
        ...data,
        // @ts-expect-error
        tags: selectedTagIds,
        // diseaseTags field is deprecated, use tags instead
      }),
      success: "Blog Updated Succesfully ",
      onSuccess: () => {
        methods.reset();
        router.replace("/blogs");
        blogs.refetch();
        setOpen(false);
      },
    });
  };

  const onBlogSelect = (blog: RouterOutput["blog"]["getBlogs"][number]) => {
    console.log((blog.tags || []).map((_) => String((_.tag as any)._id)));

    methods.setValue("title", blog.title);
    methods.setValue("author", blog.author);
    methods.setValue("banner", blog.banner as string);
    methods.setValue("blogId", String(blog._id));
    methods.setValue("contentHtml", blog.contentHtml);
    methods.setValue("description", blog.description);
    methods.setValue("publishDate", blog.publishDate);
    // diseaseTags field is deprecated, use tags instead
    methods.setValue("status", blog.status);
    methods.setValue("editorData", blog.editorData);
    methods.setValue(
      "tags",
      // @ts-ignore
      (blog.tags || []).map((_) => String(_.tag))
    );
    methods.setValue("timeToReadInMins", blog.timeToReadInMins as number);

    setOpen(true);
  };

  return (
    <div className="container flex flex-col  ">
      <div className="flex  justify-end mt-5 gap-4">
        <Button>
          <Link href={"/blogs/create"}>Create Blog</Link>
        </Button>
        <Button>
          <Link href={"/blogs/manage"}>Dashboard</Link>
        </Button>
      </div>

      {/* Update Blogpost dialog */}
      <AlertDialog open={open} onOpenChange={setOpen}>
        <AlertDialogContent className="min-w-[80vw] ">
          {/* Close button */}
          <button
            onClick={() => setOpen(false)}
            className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none"
            aria-label="Close"
          >
            <X className="h-4 w-4" />
            <span className="sr-only">Close</span>
          </button>

          <AlertDialogHeader>
            <AlertDialogTitle>Update Blog Post</AlertDialogTitle>
          </AlertDialogHeader>
          <div className="max-h-[70vh] overflow-y-scroll">
            <ManageBlogPostForm
              methods={methods}
              blogTags={blogTags.data || []}
              onFormSubmit={handleSubmit}
            />
          </div>
        </AlertDialogContent>
      </AlertDialog>

      <Table className="mt-20">
        <TableHeader>
          <TableRow>
            <TableHead>Blog Title</TableHead>
            <TableHead>Author</TableHead>
            <TableHead>Tags</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Publish Date</TableHead>
            <TableHead>Action</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {blogs.data?.map((b) => {
            return (
              <TableRow key={String(b._id)}>
                <TableCell>{b.title}</TableCell>
                <TableCell>{b.author}</TableCell>
                <TableCell className="space-x-2">
                  {b.tags?.map((t) => {
                    return <Badge key={String(t._id)}>{t.tag}</Badge>;
                  })}
                </TableCell>
                <TableCell>{b.status}</TableCell>
                <TableCell>
                  {b.publishDate && format(b.publishDate, "PPP")}
                </TableCell>
                <TableCell className="flex items-center gap-2">
                  <Button onClick={() => onBlogSelect(b)}>
                    <Edit />
                    Update
                  </Button>
                  <Button
                    size="icon"
                    onClick={() =>
                      toastPromise({
                        asyncFunc: deleteBlog.mutateAsync(String(b._id)),
                        success: "blog deleted successfully",
                        onSuccess() {
                          blogs.refetch();
                        },
                      })
                    }
                  >
                    <Trash />
                  </Button>
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
}
