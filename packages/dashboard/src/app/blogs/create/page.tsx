"use client";
import { BlockNoteView, useCreateBlockNote } from "@blocknote/react";
import React, { useMemo, useState } from "react";
import { z } from "zod";
import {
  createBlogTagValidator,
  manageBlogValidator,
} from "packages/shared/validators/blog";
import { <PERSON><PERSON><PERSON><PERSON>, SubmitHandler, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { trpc } from "@web/providers/Providers";
import FormField from "@web/components/form/FormField";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  Di<PERSON>Header,
  Di<PERSON>T<PERSON>le,
  DialogTrigger,
} from "@web/components/ui/dialog";
import { Button } from "@web/components/ui/button";
import { toastPromise } from "@web/lib/utils";
import { FormDatePicker } from "@web/components/form/FormDatePicker";
import { FormSelect } from "@web/components/form/FormSelect";
import { BlogStatus } from "packages/shared/types/Blog";
import { Label } from "@web/components/ui/label";
import { FormComboBoxPopover } from "@web/components/form/FormComboPopover";
import S3FileUpload from "@web/components/form/S3FileUpload";
import { ScrollArea } from "@web/components/ui/scroll-area";
import ManageBlogPostForm from "@web/elements/form/ManageBlogPost";
import MarkdownPreview from "@uiw/react-markdown-preview";
import { useRouter } from "next/navigation";
import { Input } from "@web/components/ui/input";

type BlogForm = z.infer<typeof manageBlogValidator>;

type BlogTagForm = z.infer<typeof createBlogTagValidator>;

export default function Learn() {
  const router = useRouter();
  const [blogTagModal, setBlogTagModal] = useState(false);
  const methods = useForm<BlogForm>({
    resolver: zodResolver(manageBlogValidator),
    defaultValues: {
      author: "",
      banner: "",
      contentHtml: "",
      description: "",
      publishDate: "" as unknown as Date,
      status: BlogStatus.DRAFT,
      tags: [],
      timeToReadInMins: "" as unknown as number,
      title: "",
      editorData: [],
      // diseaseTags field is deprecated, use tags instead
    },
  });

  const createBlogTagsMethod = useForm<BlogTagForm>({
    resolver: zodResolver(createBlogTagValidator),
    defaultValues: {
      tag: "",
      scope: [],
    },
  });

  const createBlog = trpc.blog.createBlog.useMutation();
  const createBlogTag = trpc.blog.createBlogTag.useMutation();

  const blogTags = trpc.blog.tags.useQuery();

  const handleCreateBlogTagSubmit = createBlogTagsMethod.handleSubmit((data) =>
    toastPromise({
      asyncFunc: createBlogTag.mutateAsync(data),
      success: "Tag Created Succesfully ",
      onSuccess: () => {
        createBlogTagsMethod.reset();
        blogTags.refetch();
        setBlogTagModal(false);
      },
    })
  );
  const handleCreateBlogSubmit: SubmitHandler<BlogForm> = (data) =>
    toastPromise({
      asyncFunc: createBlog.mutateAsync(data),
      success: "Blog Created Succesfully ",
      onSuccess: () => {
        methods.reset();
        router.replace("/learn");
      },
    });

  return (
    <div className="flex h-[calc(100vh-105px)] gap-5 mt-7 ">
      <Dialog
        open={blogTagModal}
        onOpenChange={(v) => {
          createBlogTagsMethod.reset();
          setBlogTagModal(v);
        }}
      >
        <DialogTrigger asChild className="absolute right-10">
          <Button>Create Tag</Button>
        </DialogTrigger>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create tag</DialogTitle>
          </DialogHeader>
          <FormProvider {...createBlogTagsMethod}>
            <form className="space-y-4">
              <FormField name="tag" label="Tag" placeholder="Add Tag Here" />
              <div className="space-y-2">
                <Label>Scope (Optional)</Label>
                <p className="text-sm text-muted-foreground">
                  Add scope values like rabbles, blogs, connect, etc.
                </p>
                {/* Note: For simplicity, we'll use a text input for comma-separated values */}
                <Input
                  placeholder="Enter scope values separated by commas"
                  onChange={(e) => {
                    const scopes = e.target.value
                      .split(",")
                      .map((s) => s.trim())
                      .filter(Boolean);
                    createBlogTagsMethod.setValue("scope", scopes);
                  }}
                />
              </div>
            </form>
          </FormProvider>
          <DialogFooter>
            <Button onClick={handleCreateBlogTagSubmit}>Submit</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      <div className="flex gap-4">
        <div className="ml-4 mb-5">
          <h2 className="text-primary text-xl font-semibold">Create Blog</h2>
          <ScrollArea className="h-[calc(100vh-200px)] w-[38vw] p-4 border">
            <ManageBlogPostForm
              methods={methods}
              blogTags={blogTags.data || []}
              onFormSubmit={handleCreateBlogSubmit}
            />
          </ScrollArea>
        </div>
        <div>
          <h2 className="text-primary text-xl font-semibold">
            Blog Content Preview
          </h2>
          <ScrollArea className="h-[calc(100vh-200px)] w-[300px] p-4 border">
            <MarkdownPreview
              source={methods.watch("contentHtml")}
              className="bg-white"
              wrapperElement={{ "data-color-mode": "light" }}
            />
          </ScrollArea>
        </div>
      </div>
    </div>
  );
}
