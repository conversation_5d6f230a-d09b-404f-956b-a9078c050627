{"name": "rabble_monorepo", "version": "1.0.0", "main": "index.js", "license": "MIT", "workspaces": ["packages/*", "app/*"], "private": true, "scripts": {"dev": "concurrently \"yarn workspace rabble_be dev\" \"yarn workspace fe dev\"", "app": "yarn workspace rabble start & yarn workspace rabble_be dev", "app-on-mobile": "export EXPO_PUBLIC_IPV4_ADDRESS=$(ifconfig | grep \"inet \" | grep -Fv 127.0.0.1 | awk '{print $2}' | head -n 1) && yarn workspace rabble start", "pull:submodules": "git submodule update --init --recursive", "build": "yarn workspace rabble_be build"}, "devDependencies": {"concurrently": "^8.2.2"}, "nohoist": ["**/react-native", "**/react-native/**", "**/@react-native/**", "**/@react-native/virtualized-lists"]}